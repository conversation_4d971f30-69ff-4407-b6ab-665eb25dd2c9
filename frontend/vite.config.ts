import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig(() => {
  // Get path prefix from environment variable, default to '/'
  const pathPrefix = process.env.VITE_PATH_PREFIX || '/'

  // Get API base URL from environment variable
  const apiBaseUrl = process.env.VITE_API_BASE_URL || 'http://localhost:8001'

  return {
    plugins: [react(), tailwindcss()],
    base: pathPrefix,
    server: {
      port: 3000,
      proxy: {
        '/api': {
          target: apiBaseUrl,
          changeOrigin: true,
        },
      },
    },
    define: {
      __PATH_PREFIX__: JSON.stringify(pathPrefix),
      __API_BASE_URL__: JSON.stringify(apiBaseUrl),
    },
  }
})
