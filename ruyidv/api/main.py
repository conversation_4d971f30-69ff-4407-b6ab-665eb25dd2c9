"""
FastAPI main application
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from ..config import settings
from .middleware.security import (
    InputValidationMiddleware,
    RequestLoggingMiddleware,
    SecurityHeadersMiddleware,
)
from .routers import admin, annotation, auth, batches, samples, stats

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加安全中间件 (按执行顺序添加)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(InputValidationMiddleware)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix=settings.API_V1_PREFIX)
app.include_router(stats.router, prefix=settings.API_V1_PREFIX)
app.include_router(annotation.router, prefix=settings.API_V1_PREFIX)
app.include_router(samples.router, prefix=settings.API_V1_PREFIX)
app.include_router(batches.router, prefix=settings.API_V1_PREFIX)
app.include_router(admin.router, prefix=settings.API_V1_PREFIX)


@app.get("/")
async def root():
    """API根端点"""
    return {
        "message": "Ruyi Dataverse API",
        "version": settings.VERSION,
        "docs": "/docs",
    }


# Static file serving for frontend
if settings.SERVE_FRONTEND and settings.FRONTEND_STATIC_DIR.exists():
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import FileResponse
    from fastapi.exceptions import HTTPException

    # Normalize path prefix
    path_prefix = settings.FRONTEND_PATH_PREFIX.rstrip('/') if settings.FRONTEND_PATH_PREFIX != '/' else ''

    # Mount static files for assets
    app.mount(
        f"{path_prefix}/assets",
        StaticFiles(directory=settings.FRONTEND_STATIC_DIR / "assets"),
        name="assets"
    )

    # Serve other static files (like vite.svg)
    @app.get(f"{path_prefix}/vite.svg")
    async def serve_vite_svg():
        file_path = settings.FRONTEND_STATIC_DIR / "vite.svg"
        if file_path.exists():
            return FileResponse(file_path)
        raise HTTPException(status_code=404, detail="File not found")

    # SPA fallback route - serve index.html for all non-API routes
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        # Don't serve SPA for API routes, docs, or assets
        if (full_path.startswith("api/") or
            full_path.startswith("docs") or
            full_path.startswith("redoc") or
            full_path.startswith("assets/")):
            raise HTTPException(status_code=404, detail="Not found")

        # For root path prefix, serve all remaining routes
        if path_prefix == '':
            index_path = settings.FRONTEND_STATIC_DIR / "index.html"
            if index_path.exists():
                return FileResponse(index_path)
            raise HTTPException(status_code=404, detail="Frontend not found")

        # For non-root path prefix, check if path starts with prefix
        prefix_without_slash = path_prefix.lstrip('/')
        if not full_path.startswith(prefix_without_slash):
            raise HTTPException(status_code=404, detail="Not found")

        # Serve index.html for SPA routing
        index_path = settings.FRONTEND_STATIC_DIR / "index.html"
        if index_path.exists():
            return FileResponse(index_path)
        raise HTTPException(status_code=404, detail="Frontend not found")
