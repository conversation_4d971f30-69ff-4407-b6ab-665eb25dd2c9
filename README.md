# Ruyi Dataverse

> A comprehensive training data management system for Ruyi Grounding VLM (Vision-Language Model) with advanced annotation capabilities.

[![Python 3.12+](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![UV Package Manager](https://img.shields.io/badge/Package%20Manager-UV-green.svg)](https://github.com/astral-sh/uv)
[![FastAPI](https://img.shields.io/badge/API-FastAPI-009688.svg)](https://fastapi.tiangolo.com/)
[![SQLAlchemy](https://img.shields.io/badge/ORM-SQLAlchemy-red.svg)](https://sqlalchemy.org/)
[![React](https://img.shields.io/badge/Frontend-React-61DAFB.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC.svg)](https://www.typescriptlang.org/)

## 🎯 Overview

**Ruyi Dataverse** is a comprehensive training data management system specifically designed for multimodal AI models. It provides:

- **Multi-modal Training Data Management**: Unified management of images with automatic deduplication
- **Six Training Modes**: Support for grounding, description, enumeration, checklist, and ensure modes
- **Advanced Annotation System**: Web-based annotation interface with session management and authentication
- **Admin Dashboard**: Real-time monitoring and management interface with system health tracking
- **Flexible Import/Export**: JSON import and ShareGPT format export with custom templates
- **Batch Organization**: Version control and management through batch system
- **Rich CLI Interface**: Complete command-line tools for all operations
- **Secure Authentication**: Session-based authentication with admin-controlled access
- **RESTful API**: Production-ready FastAPI backend with comprehensive endpoints

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- [UV Package Manager](https://github.com/astral-sh/uv) (recommended) or pip
- PostgreSQL 12+ (see [PostgreSQL Setup Guide](doc/postgresql-setup.md))

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd Ruyi-Dataverse

# Install dependencies with UV (recommended)
uv sync

# Or with pip
pip install -e .
```

### Initialize System

```bash
# Setup PostgreSQL database (see doc/postgresql-setup.md for details)
# Quick setup:
sudo -u postgres createuser -P ruyidv  # Enter password: ruyidv
sudo -u postgres createdb -O ruyidv ruyidv

# Initialize the system (creates database tables and storage directories)
uv run ruyidv db upgrade

# Verify installation
uv run ruyidv --help
uv run ruyidv db status
```

### Basic Usage

```bash
# 1. Create a batch for your training data
uv run ruyidv batch create "My First Batch" --description "Initial training data"

# 2. Import data from JSON file
uv run ruyidv import json data.json

# 3. Export training data
uv run ruyidv export data --batch-id 1 --format sharegpt --output training_data.json

# 4. View statistics
uv run ruyidv stats overview
```

### Annotation Workflow

```bash
# 1. Create annotation session (admin-only via CLI)
uv run ruyidv admin create-session "Project Alpha" --annotator "annotator1" --batch-ids "1,2"

# 2. Start the API server
uv run ruyidv serve --reload

# 3. Start the frontend (in another terminal)
cd frontend && npm run dev

# 4. Annotator accesses session via web interface
# Access: http://localhost:5173 (login with session credentials)

# 5. Export annotation results (admin-only)
uv run ruyidv annotation export-session 1 --output corrections.json

# 6. Apply corrections to database
uv run ruyidv annotation apply corrections.json
```

## 📊 Core Concepts

### Batches
Organizational units for training data that allow version control and management. Each batch contains multiple samples.

### Images
Training images are automatically deduplicated using SHA256 hashes and stored in an organized directory structure.

### Samples
Individual training examples that link images with metadata according to specific training modes.

### Training Modes
Six different modes for various AI training tasks:

## 🎭 Training Modes

### 1. Grounding
**Purpose**: Find objects in images based on text descriptions.

**Input**: Text description → **Output**: Bounding box coordinates

**Metadata Structure**:
```json
{
  "description": "red car in the parking lot",
  "coordinate": [100, 50, 200, 150]
}
```
*Note: Coordinates are in pixel values [x1, y1, x2, y2] relative to the original image dimensions.*

### 2. Describe
**Purpose**: Describe what's in a specific region of an image.

**Input**: Bounding box coordinates → **Output**: Text description

**Metadata Structure**:
```json
{
  "coordinate": [100, 50, 100, 50],
  "description": "A red sedan parked between two white cars"
}
```
*Note: Describe mode uses point coordinates [x0, y0, x0, y0] in pixel values relative to the original image dimensions.*

### 3. Enumerate Text
**Purpose**: List all text elements visible in an image.

**Input**: Image → **Output**: List of text elements

**Metadata Structure**:
```json
{
  "general_description": "Street signs and storefront text",
  "elements": [
    "STOP",
    "Main Street",
    "Coffee Shop",
    "OPEN"
  ]
}
```

### 4. Enumerate Coordinate
**Purpose**: List coordinates of all specified elements in an image.

**Input**: Image → **Output**: List of coordinates

**Metadata Structure**:
```json
{
  "general_description": "All visible cars in the parking lot",
  "elements": [
    [50, 30, 120, 80],
    [150, 40, 220, 90],
    [250, 35, 320, 85]
  ]
}
```
*Note: Coordinates are in pixel values [x1, y1, x2, y2] relative to the original image dimensions.*

### 5. Checklist
**Purpose**: Verify presence/absence of multiple items in an image.

**Input**: List of items to check → **Output**: Boolean results

**Metadata Structure**:
```json
{
  "descriptions": [
    "Is there a red car?",
    "Are there people walking?",
    "Is it daytime?"
  ],
  "results": [true, false, true]
}
```

### 6. Ensure
**Purpose**: Identify actions needed to achieve a desired GUI state.

**Input**: Expected state → **Output**: Click coordinates (or null if already achieved)

**Metadata Structure**:
```json
{
  "expected_state": "Login dialog is open",
  "coordinate": [300, 150, 350, 180]
}
```
*Note: Coordinates are in pixel values [x1, y1, x2, y2] relative to the original image dimensions.*

## 🎨 Annotation Interface Features

### Interactive Annotation Tools
- **Canvas-based Drawing**: Precise bounding box drawing with mouse interaction
- **Pixel-Perfect Coordinates**: All coordinates stored as actual pixel values for maximum precision
- **Real-time Preview**: Live preview of annotations as you draw
- **Box Selection & Editing**: Click to select existing boxes, drag to redraw
- **Keyboard Shortcuts**: Delete key to remove selected boxes

### Mode-Specific Interfaces
- **Grounding & Ensure**: Single bounding box with description editing
- **Enumerate Coord**: Multiple bounding boxes with element management
- **Describe**: Bounding box display with description editing
- **Enumerate Text**: Text list management with add/remove capabilities
- **Checklist**: Interactive checkbox interface with presence/absence marking

### Annotation Workflow
- **Sample Navigation**: Previous/Next buttons with sample counter
- **Progress Tracking**: Real-time progress bars and completion statistics
- **Correction Mode**: Toggle between view and edit modes for metadata correction
- **Auto-navigation**: Automatic navigation to first unannotated sample
- **Session Management**: Create sessions with sample filters and annotator assignment

## 🛡️ Admin Dashboard

The admin dashboard provides a comprehensive web interface for real-time monitoring and management of the annotation system.

### Dashboard Setup

```bash
# Setup admin dashboard with custom path and password
uv run ruyidv admin setup-dashboard --path "admin-panel" --password "secure-password" --confirm

# Setup with auto-generated credentials (recommended for security)
uv run ruyidv admin setup-dashboard --confirm

# View current dashboard configuration
uv run ruyidv admin show-dashboard

# Update dashboard password
uv run ruyidv admin setup-dashboard --password "new-password" --confirm

# Remove dashboard access
uv run ruyidv admin remove-dashboard --confirm
```

### Dashboard Features

#### 📊 **System Overview**
- **Real-time Statistics**: Live metrics for batches, samples, sessions, and annotations
- **Session Status Distribution**: Visual breakdown of session states (pending, in-progress, completed)
- **Mode Distribution**: Annotation mode usage patterns and statistics
- **Top Performing Sessions**: Completion rates and progress tracking
- **Recent Activity Summary**: 24-hour annotation metrics and trends

#### 👥 **Session Management**
- **Comprehensive Session Listing**: All annotation sessions with filtering by status
- **Detailed Progress Tracking**: Visual progress bars and completion percentages
- **Session Details Modal**: Complete session information including annotator details
- **Real-time Status Updates**: Live monitoring of session activity
- **Performance Analytics**: Completion rates and activity timestamps

#### 📈 **Activity Monitor**
- **Live Activity Feed**: Real-time stream of annotation activities and system events
- **Configurable Time Ranges**: Monitor activity from 1 hour to 1 week
- **Activity Type Filtering**: Annotations, logins, access events, and system activities
- **Performance Metrics**: Annotations per hour, active users, and session statistics
- **Auto-refresh Controls**: Configurable real-time updates (10-second intervals)

#### 🔧 **System Health**
- **Resource Monitoring**: CPU, memory, and disk usage with visual indicators
- **Database Health**: Session counts, annotation metrics, and database size
- **Storage Tracking**: File system usage and storage path information
- **Performance Recommendations**: Automated suggestions based on system thresholds
- **Overall Status**: Health indicators with issue detection and alerts

### Dashboard Access

The admin dashboard uses path-based routing for enhanced privacy:

```
# Dashboard URL format
http://{host}:{port}/{dashboard_path}

# Example URLs
http://localhost:8001/admin-panel/login      # Login page
http://localhost:8001/admin-panel/dashboard  # Main dashboard

# With auto-generated path (16-character random string)
http://localhost:8001/hvdpzsfd9gi48wi/login
```

### Security Features

- **Single Admin System**: Only one admin dashboard can exist at a time
- **Path-based Privacy**: Random URL paths for enhanced security
- **Session Management**: 4-hour session timeout with 2-hour idle timeout
- **Audit Logging**: All admin activities are logged with IP addresses
- **Secure Authentication**: BCrypt password hashing and session validation

## 🛠️ CLI Commands

### Batch Management

```bash
# Create a new batch
uv run ruyidv batch create "Batch Name" --description "Description"

# List all batches
uv run ruyidv batch list

# Show batch details
uv run ruyidv batch show <batch_id>

# Delete a batch
uv run ruyidv batch delete <batch_id>

# Show batch statistics
uv run ruyidv batch stats <batch_id>
```

### Data Import

```bash
# Import from JSON file (uses soft links by default)
uv run ruyidv import json data.json --image-base-path ./images

# Import with file copying instead of soft links
uv run ruyidv import json data.json --image-base-path ./images --copy-images

# Validate import file without importing
uv run ruyidv import validate data.json

# Check import status
uv run ruyidv import status
```

### Data Export

```bash
# Export training data
uv run ruyidv export data --batch-id 1 --format sharegpt --output training.json

# Export with filters
uv run ruyidv export data --mode grounding --labels "vehicle,outdoor" --limit 1000

# List available formatters
uv run ruyidv export list-formatters

# Create custom formatter example
uv run ruyidv export create-example
```

### Statistics and Analytics

```bash
# System overview
uv run ruyidv stats overview

# Batch statistics
uv run ruyidv stats batch <batch_id>

# Mode distribution
uv run ruyidv stats modes

# Label statistics
uv run ruyidv stats labels --top 20
```

### Database Management

```bash
# Run database migrations
uv run ruyidv db migrate

# Reset database (⚠️ destructive)
uv run ruyidv db reset

# Backup database
uv run ruyidv db backup backup.sql
```

### Configuration

```bash
# Show current configuration
uv run ruyidv config show

# Set configuration values
uv run ruyidv config set storage_path=/path/to/storage
```

## 📁 Data Formats

### Import JSON Format

```json
{
  "batch_name": "Training Batch 1",
  "batch_description": "Initial grounding training data",
  "samples": [
    {
      "image_path": "images/sample1.jpg",
      "mode": "grounding",
      "metadata": {
        "description": "red car",
        "coordinate": [100, 50, 200, 150]
      },
      "labels": ["vehicle", "red", "outdoor"]
    },
    {
      "image_path": "images/sample2.jpg",
      "mode": "describe",
      "metadata": {
        "coordinate": [150, 75, 300, 200],
        "description": "A person walking on the sidewalk"
      },
      "labels": ["person", "outdoor"]
    }
  ]
}
```
*Note: All coordinates in the import format are pixel values [x1, y1, x2, y2] relative to the original image dimensions.*

### Export ShareGPT Format

```json
[
  {
    "instruction": "Find the red car in this image",
    "input": "",
    "output": "[100, 50, 200, 150]",
    "images": ["/storage/images/abc123/sample1.jpg"]
  },
  {
    "instruction": "Describe what you see in the highlighted region",
    "input": "",
    "output": "A person walking on the sidewalk",
    "images": ["/storage/images/def456/sample2.jpg"]
  }
]
```

## 🌐 Web API & Annotation System

### API Server

Start the API server:

```bash
# Start development server
uv run ruyidv serve --reload

# Production server
uv run ruyidv serve --host 0.0.0.0 --port 8000
```

Access the API documentation at: `http://localhost:8000/docs`

### Annotation System

The annotation system provides comprehensive validation and correction capabilities for training samples through both CLI and web interface with secure session-based authentication.

#### Authentication & Access Control

- **Admin Access**: CLI access = admin access (no separate admin accounts needed)
- **Session-Based Authentication**: Each annotation session has unique credentials
- **Data Isolation**: Annotators can only access their assigned sessions
- **Secure Credentials**: BCrypt password hashing with auto-generated secure passwords

#### Annotation Workflow

1. **Create Annotation Session** (Admin-only via CLI):
   ```bash
   # Create session with auto-generated credentials
   uv run ruyidv admin create-session "Project Alpha" --annotator "annotator1" --batch-ids "1,2"

   # Create session with custom credentials
   uv run ruyidv admin create-session "Project Beta" --annotator "annotator2" \
     --username "custom_user" --password "secure_password"
   ```

2. **Annotate Samples** (via Web Interface):
   ```bash
   # Start the frontend (in development)
   cd frontend && npm run dev
   # Access: http://localhost:5173
   # Login with session credentials provided by admin
   ```

3. **Export & Apply Corrections** (Admin-only via CLI):
   ```bash
   # Export annotation results
   uv run ruyidv annotation export-session 1 --output corrections.json

   # Preview changes before applying
   uv run ruyidv annotation preview corrections.json

   # Apply corrections to database
   uv run ruyidv annotation apply corrections.json

   # Rollback if needed
   uv run ruyidv annotation rollback <operation-id>
   ```

#### Admin Commands

```bash
# Admin Dashboard Management
uv run ruyidv admin setup-dashboard --path "admin-panel" --password "secure-password" --confirm
uv run ruyidv admin setup-dashboard --confirm  # Auto-generate credentials
uv run ruyidv admin show-dashboard
uv run ruyidv admin remove-dashboard --confirm

# Session Management
uv run ruyidv admin create-session <name> --annotator <annotator>
uv run ruyidv admin delete-session <session-id>
uv run ruyidv admin set-session-password --session-id <id> --username <name>

# Annotation Management
uv run ruyidv annotation list-sessions
uv run ruyidv annotation session-info <session-id>
uv run ruyidv annotation validate corrections.json
uv run ruyidv annotation list-operations
```

## ⚙️ Configuration

### Configuration File

Create `~/.ruyidv/config.yaml`:

```yaml
database_url: "sqlite:///./ruyidv.db"
storage_path: "./storage"
api:
  host: "127.0.0.1"
  port: 8000
  debug: false
image:
  max_file_size: 52428800  # 50MB
  allowed_extensions: [".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff"]
export:
  default_templates:
    instruction: "{{ description }}"
    output: "{{ coordinate }}"
```

### Environment Variables

```bash
export RUYIDV_DATABASE_URL="sqlite:///./ruyidv.db"
export RUYIDV_STORAGE_PATH="./storage"
export RUYIDV_HOST="127.0.0.1"
export RUYIDV_PORT="8000"
```

## 🏗️ Project Structure

```
ruyidv/                     # Core business logic
├── models.py              # Data models (Batch, Image, Sample, AnnotationSession, etc.)
├── schemas.py             # API data schemas and validation
├── config.py              # System configuration management
├── database.py            # Database connection and setup
├── api/                   # REST API interfaces
│   ├── main.py           # FastAPI application with security middleware
│   ├── middleware/       # Security and logging middleware
│   ├── dependencies.py   # Authentication dependencies
│   └── routers/          # API route handlers
│       ├── annotation.py # Annotation session endpoints
│       ├── auth.py       # Authentication endpoints
│       ├── batches.py    # Batch management endpoints
│       ├── samples.py    # Sample management endpoints
│       └── stats.py      # Statistics endpoints
├── services/              # Business service layer
│   ├── annotation_service.py        # Annotation workflow management
│   ├── annotation_export_service.py # Export annotation results
│   ├── annotation_apply_service.py  # Apply corrections to database
│   ├── auth_service.py              # Authentication and session management
│   ├── batch_service.py             # Batch operations
│   ├── image_service.py             # Image processing and storage
│   ├── import_service.py            # Data import functionality
│   ├── export_service.py            # Data export with custom formatters
│   └── sample_service.py            # Sample CRUD operations
├── cli/                   # Command-line interface
│   └── commands/         # CLI command modules
│       ├── admin.py      # Admin management commands
│       ├── annotation.py # Annotation CLI commands
│       ├── batch.py      # Batch management commands
│       ├── export.py     # Export commands
│       ├── import_cmd.py # Import commands
│       ├── serve.py      # API server commands
│       ├── stats.py      # Statistics commands
│       └── system.py     # System initialization
└── __init__.py

frontend/                   # React TypeScript annotation interface
├── src/
│   ├── components/       # Reusable UI components
│   │   ├── annotation/   # Mode-specific annotation components
│   │   ├── InteractiveImageCanvas.tsx  # Bounding box drawing
│   │   ├── Layout.tsx    # Application layout
│   │   └── ProtectedRoute.tsx         # Route protection
│   ├── contexts/        # React contexts
│   │   └── AuthContext.tsx           # Authentication context
│   ├── pages/           # Main application pages
│   │   ├── AnnotationWorkspace.tsx   # Core annotation interface
│   │   ├── Login.tsx                 # Session login page
│   │   └── Sessions.tsx              # Session management
│   ├── services/        # API client and utilities
│   │   └── api.ts       # API client configuration
│   └── utils/           # Coordinate conversion utilities
│       └── coordinates.ts            # Coordinate normalization
doc/                       # Project documentation
├── annotation_system_design.md      # Annotation system architecture
├── AUTHENTICATION_PLAN.md           # Authentication implementation plan
├── PHASE2_SERVICES_STATUS.md        # Services implementation status
└── ...                              # Other documentation
alembic/                   # Database migrations
├── versions/            # Migration files
└── env.py              # Alembic configuration
storage/                   # Image file storage
├── images/             # Organized image storage (hash-based)
├── exports/            # Export file output
└── imports/            # Import file staging
```

## 🔧 Development

### Development Setup

```bash
# Clone and setup backend
git clone <repository-url>
cd Ruyi-Dataverse
uv sync --dev

# Run database migrations
uv run alembic upgrade head

# Run development server
uv run ruyidv serve --reload

# Setup frontend (in another terminal)
cd frontend
npm install
npm run dev

# Run linting and formatting
uv run ruff check .
uv run ruff format .

# Frontend type checking
cd frontend && npx tsc --noEmit
```

### Custom Formatters

Create custom export formatters:

```bash
# Generate example formatter
uv run ruyidv export create-example

# Edit the formatter file
nano ~/.ruyidv/formatters/example_formatters.py

# Reload formatters
uv run ruyidv export reload
```

## 📈 Features

### Core Data Management
- ✅ **Multi-modal Data Management**: Images + metadata with automatic deduplication
- ✅ **Six Training Modes**: Complete support for all VLM training scenarios
- ✅ **Flexible Import/Export**: JSON import, ShareGPT export with custom templates
- ✅ **Rich CLI Interface**: Beautiful terminal interface with progress bars
- ✅ **Batch Organization**: Version control and management
- ✅ **Custom Formatters**: Extensible export system with image processing capabilities
- ✅ **Data Validation**: Comprehensive validation for all training modes
- ✅ **Statistics & Analytics**: Detailed insights into your training data

### Annotation System
- ✅ **Web-based Annotation Interface**: React TypeScript frontend with Tailwind CSS
- ✅ **Interactive Annotation Tools**: Canvas-based bounding box drawing and editing
- ✅ **Mode-specific Components**: Specialized interfaces for all six training modes
- ✅ **Session Management**: Create and manage annotation sessions with progress tracking
- ✅ **Sample Navigation**: Navigate between samples with revision capabilities
- ✅ **Correction Workflow**: Mark samples as correct/incorrect with metadata corrections
- ✅ **Export & Apply System**: CLI-based export with changelog and rollback support
- ✅ **Data Integrity**: Optimistic locking and comprehensive validation
- ✅ **Pixel-Perfect Coordinates**: Actual pixel coordinates for maximum precision and accuracy

### Admin Dashboard & Monitoring
- ✅ **Real-time Admin Dashboard**: React TypeScript interface for system monitoring
- ✅ **System Health Monitoring**: CPU, memory, disk usage with performance recommendations
- ✅ **Session Management Interface**: Visual session tracking with progress monitoring
- ✅ **Live Activity Feed**: Real-time annotation activity and system event monitoring
- ✅ **Performance Analytics**: Completion rates, annotation throughput, and user activity
- ✅ **Path-based Security**: Random URL paths for enhanced admin dashboard privacy

### Authentication & Security
- ✅ **Session-based Authentication**: Secure credential system for annotators
- ✅ **Admin Dashboard Authentication**: Single admin system with secure path-based access
- ✅ **Admin-controlled Access**: CLI-only session creation and management
- ✅ **Data Isolation**: Complete separation between annotation sessions
- ✅ **BCrypt Password Hashing**: Secure password storage and verification
- ✅ **Audit Logging**: Comprehensive logging of all authentication and admin activities
- ✅ **Audit Logging**: Comprehensive logging of authentication and annotation events
- ✅ **Security Middleware**: Request validation, logging, and security headers

### API & Infrastructure
- ✅ **RESTful API**: FastAPI-based backend with comprehensive endpoints
- ✅ **Database Migrations**: Alembic-based schema management
- ✅ **CORS Configuration**: Proper frontend-backend communication setup
- ✅ **Environment Configuration**: YAML and environment variable support
- ✅ **Production Ready**: Security middleware and proper error handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🙏 Acknowledgments

- Built for the Ruyi Grounding VLM project
- Powered by FastAPI, SQLAlchemy, and Click
- Frontend built with React, TypeScript, and Tailwind CSS
- Image processing with Pillow
- Template engine with Jinja2
- Authentication with BCrypt and python-jose
