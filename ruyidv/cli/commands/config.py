"""
Configuration management commands
"""

import os
import shutil
import subprocess
from pathlib import Path
from typing import Any, Dict

import click
import yaml
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table

from ruyidv.config import settings

console = Console()

# 配置文件路径
CONFIG_DIR = Path.home() / ".ruyidv"
CONFIG_FILE = CONFIG_DIR / "config.yaml"

# 默认配置
DEFAULT_CONFIG = {
    "database": {
        "url": "postgresql://ruyidv:ruyidv@localhost:5432/ruyidv",
    },
    "storage": {
        "root": "./storage",
    },
    "import": {
        "default_image_base_path": None,
        "validate_images": True,
        "max_batch_size": 1000,
    },
    "export": {
        "default_format": "sharegpt",
        "default_limit": 1000,
        "templates": {
            "instruction": "{{ description }}",
            "output": "{{ coordinate }}",
        },
    },
    "api": {
        "host": "127.0.0.1",
        "port": 8000,
        "debug": False,
    },
}


def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    if CONFIG_FILE.exists():
        try:
            with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                user_config = yaml.safe_load(f) or {}

            # 合并默认配置和用户配置
            config = DEFAULT_CONFIG.copy()
            _deep_update(config, user_config)
            return config
        except Exception as e:
            console.print(f"[yellow]⚠️ Error loading config file: {e}[/yellow]")
            console.print("[yellow]Using default configuration...[/yellow]")

    return DEFAULT_CONFIG.copy()


def save_config(config: Dict[str, Any]) -> bool:
    """保存配置文件"""
    try:
        CONFIG_DIR.mkdir(exist_ok=True)
        with open(CONFIG_FILE, "w", encoding="utf-8") as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        return True
    except Exception as e:
        console.print(f"[red]❌ Error saving config: {e}[/red]")
        return False


def _deep_update(base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
    """深度更新字典"""
    for key, value in update_dict.items():
        if (
            key in base_dict
            and isinstance(base_dict[key], dict)
            and isinstance(value, dict)
        ):
            _deep_update(base_dict[key], value)
        else:
            base_dict[key] = value


def _get_nested_value(config: Dict[str, Any], key_path: str) -> Any:
    """获取嵌套配置值"""
    keys = key_path.split(".")
    value = config
    try:
        for key in keys:
            value = value[key]
        return value
    except (KeyError, TypeError):
        return None


def _set_nested_value(config: Dict[str, Any], key_path: str, value: Any) -> bool:
    """设置嵌套配置值"""
    keys = key_path.split(".")
    current = config

    try:
        # 导航到父级字典
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            elif not isinstance(current[key], dict):
                return False
            current = current[key]

        # 设置最终值
        current[keys[-1]] = value
        return True
    except Exception:
        return False


@click.group()
def config():
    """Configuration management commands"""
    pass


@config.command()
@click.option("--env", is_flag=True, help="Also show environment variables")
def show(env: bool):
    """Show current configuration"""
    try:
        console.print("[blue]📋 Loading configuration...[/blue]")

        # 加载配置文件
        file_config = load_config()

        # 当前运行时配置（来自环境变量等）
        runtime_config = {
            "database": {
                "url": settings.DATABASE_URL,
            },
            "storage": {
                "root": str(settings.STORAGE_ROOT),
                "images_dir": str(settings.IMAGES_DIR),
            },
            "api": {
                "host": settings.HOST,
                "port": settings.PORT,
                "debug": settings.DEBUG,
            },
            "files": {
                "max_file_size": settings.MAX_FILE_SIZE,
                "allowed_extensions": list(settings.ALLOWED_IMAGE_EXTENSIONS),
            },
        }

        # 配置文件信息
        file_info_table = Table(show_header=False, box=None, padding=(0, 2))
        file_info_table.add_column("Property", style="bold cyan", width=20)
        file_info_table.add_column("Value", width=50)

        file_info_table.add_row("Config File", str(CONFIG_FILE))
        file_info_table.add_row(
            "File Exists", "✅ Yes" if CONFIG_FILE.exists() else "❌ No"
        )
        if CONFIG_FILE.exists():
            file_size = CONFIG_FILE.stat().st_size
            file_info_table.add_row("File Size", f"{file_size} bytes")
            file_info_table.add_row(
                "Last Modified", CONFIG_FILE.stat().st_mtime.__str__()
            )

        console.print(
            Panel(file_info_table, title="📁 Configuration File", border_style="blue")
        )

        # 运行时配置
        runtime_table = Table(show_header=True, header_style="bold green")
        runtime_table.add_column("Configuration Key", style="bold", width=30)
        runtime_table.add_column("Value", width=40)
        runtime_table.add_column("Source", width=12)

        def add_config_rows(table, config_dict, section="", level=0):
            for key, value in config_dict.items():
                if isinstance(value, dict):
                    add_config_rows(
                        table,
                        value,
                        key if level == 0 else f"{section}.{key}",
                        level + 1,
                    )
                else:
                    # 检查是否来自环境变量
                    env_key = key.upper()
                    if section:
                        env_key = f"{section.upper()}_{env_key}".replace(".", "_")

                    source = "env" if env_key in os.environ else "default"

                    # 格式化值
                    if isinstance(value, list):
                        display_value = ", ".join(map(str, value))
                    elif isinstance(value, bool):
                        display_value = "✅ True" if value else "❌ False"
                    else:
                        display_value = str(value)

                    # 构建完整的键路径用于显示
                    full_key = f"{section}.{key}" if section else key

                    table.add_row(full_key, display_value, source)

        add_config_rows(runtime_table, runtime_config)
        console.print(
            Panel(runtime_table, title="⚙️ Runtime Configuration", border_style="green")
        )

        # 文件配置（如果存在且与运行时不同）
        if CONFIG_FILE.exists():
            file_table = Table(show_header=True, header_style="bold yellow")
            file_table.add_column("Configuration Key", style="bold", width=30)
            file_table.add_column("Value", width=40)

            def add_file_config_rows(table, config_dict, section="", level=0):
                for key, value in config_dict.items():
                    if isinstance(value, dict):
                        add_file_config_rows(
                            table,
                            value,
                            key if level == 0 else f"{section}.{key}",
                            level + 1,
                        )
                    else:
                        # 格式化值
                        if isinstance(value, list):
                            display_value = ", ".join(map(str, value))
                        elif isinstance(value, bool):
                            display_value = "✅ True" if value else "❌ False"
                        elif value is None:
                            display_value = "[dim]None[/dim]"
                        else:
                            display_value = str(value)

                        # 构建完整的键路径用于显示
                        full_key = f"{section}.{key}" if section else key

                        table.add_row(full_key, display_value)

            add_file_config_rows(file_table, file_config)
            console.print(
                Panel(file_table, title="📄 File Configuration", border_style="yellow")
            )

        # 环境变量（如果请求）
        if env:
            env_vars = {
                k: v
                for k, v in os.environ.items()
                if k.startswith("RUYIDV_")
                or k in ["DATABASE_URL", "STORAGE_ROOT", "HOST", "PORT", "DEBUG"]
            }

            if env_vars:
                env_table = Table(show_header=True, header_style="bold magenta")
                env_table.add_column("Environment Variable", style="bold", width=25)
                env_table.add_column("Value", width=50)

                for key, value in sorted(env_vars.items()):
                    env_table.add_row(key, value)

                console.print(
                    Panel(
                        env_table,
                        title="🌍 Environment Variables",
                        border_style="magenta",
                    )
                )
            else:
                console.print("[dim]No relevant environment variables found.[/dim]")

        # 使用提示
        console.print("\n[dim]💡 Tips:[/dim]")
        console.print(
            "[dim]• Use 'ruyidv config set <key>=<value>' to modify configuration[/dim]"
        )
        console.print(
            "[dim]• Use 'ruyidv config show --env' to see environment variables[/dim]"
        )
        console.print(f"[dim]• Config file location: {CONFIG_FILE}[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Failed to show configuration: {e}[/red]")
        raise click.Abort()


@config.command()
@click.argument("assignments", nargs=-1, required=True)
@click.option(
    "--force",
    is_flag=True,
    help="Force set without confirmation for dangerous operations",
)
def set(assignments: tuple, force: bool):
    """Set configuration values

    Examples:
        ruyidv config set database.url=sqlite:///./my_db.db
        ruyidv config set storage.root=/path/to/storage
        ruyidv config set export.templates.instruction="Find {{ description }}"
        ruyidv config set api.port=8080 api.debug=true
    """
    try:
        console.print("[blue]⚙️ Updating configuration...[/blue]")

        # 解析赋值
        updates = {}
        for assignment in assignments:
            if "=" not in assignment:
                console.print(f"[red]❌ Invalid assignment format: {assignment}[/red]")
                console.print("[yellow]Expected format: key=value[/yellow]")
                raise click.Abort()

            key, value = assignment.split("=", 1)
            key = key.strip()
            value = value.strip()

            # 类型转换
            if value.lower() in ("true", "false"):
                value = value.lower() == "true"
            elif value.isdigit():
                value = int(value)
            elif value.replace(".", "").isdigit():
                value = float(value)
            elif value.lower() == "none":
                value = None

            updates[key] = value

        # 加载当前配置
        current_config = load_config()

        # 预览更改
        preview_table = Table(show_header=True, header_style="bold cyan")
        preview_table.add_column("Key", style="bold", width=30)
        preview_table.add_column("Current", width=25)
        preview_table.add_column("New", width=25)
        preview_table.add_column("Action", width=15)

        dangerous_changes = []
        for key, new_value in updates.items():
            current_value = _get_nested_value(current_config, key)

            if current_value == new_value:
                action = "[dim]No change[/dim]"
            elif current_value is None:
                action = "[green]Add[/green]"
            else:
                action = "[yellow]Update[/yellow]"

            # 检查危险操作
            if key.startswith("database.") or key.startswith("storage."):
                dangerous_changes.append(key)

            preview_table.add_row(
                key,
                str(current_value) if current_value is not None else "[dim]None[/dim]",
                str(new_value) if new_value is not None else "[dim]None[/dim]",
                action,
            )

        console.print(
            Panel(preview_table, title="📋 Configuration Changes", border_style="blue")
        )

        # 危险操作警告
        if dangerous_changes and not force:
            console.print(
                Panel(
                    "[bold yellow]WARNING![/bold yellow] The following changes may affect system behavior:\n\n"
                    + "\n".join(f"• {key}" for key in dangerous_changes)
                    + "\n\n[yellow]Consider backing up your data before proceeding.[/yellow]",
                    title="⚠️ Potentially Dangerous Changes",
                    border_style="yellow",
                )
            )

            if not Confirm.ask("Do you want to proceed with these changes?"):
                console.print("[yellow]Configuration update cancelled.[/yellow]")
                return

        # 应用更改
        for key, value in updates.items():
            if not _set_nested_value(current_config, key, value):
                console.print(f"[red]❌ Failed to set {key}[/red]")
                raise click.Abort()

        # 保存配置
        if save_config(current_config):
            console.print(
                Panel.fit(
                    f"[green]✅ Configuration updated successfully![/green]\n\n"
                    f"Updated {len(updates)} setting(s)\n"
                    f"Config saved to: {CONFIG_FILE}",
                    title="🎉 Update Complete",
                    border_style="green",
                )
            )

            # 重启提示
            env_changes = [
                key
                for key in updates.keys()
                if key.startswith("database.")
                or key.startswith("api.")
                or key.startswith("storage.")
            ]
            if env_changes:
                console.print(
                    "\n[yellow]💡 Some changes may require restarting the application to take effect.[/yellow]"
                )
        else:
            console.print("[red]❌ Failed to save configuration[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]❌ Failed to update configuration: {e}[/red]")
        raise click.Abort()


@config.command()
@click.option("--force", is_flag=True, help="Force reset without confirmation")
def reset(force: bool):
    """Reset configuration to defaults"""
    try:
        console.print("[blue]🔄 Resetting configuration...[/blue]")

        # 警告信息
        if not force:
            console.print(
                Panel(
                    "[bold red]WARNING![/bold red] This will reset all configuration to defaults.\n\n"
                    "[red]All custom settings will be lost![/red]",
                    title="⚠️ Reset Configuration",
                    border_style="red",
                )
            )

            if not Confirm.ask("Are you sure you want to reset configuration?"):
                console.print("[yellow]Configuration reset cancelled.[/yellow]")
                return

        # 备份当前配置
        if CONFIG_FILE.exists():
            backup_file = CONFIG_FILE.with_suffix(".backup.yaml")

            shutil.copy2(CONFIG_FILE, backup_file)
            console.print(
                f"[yellow]Current config backed up to: {backup_file}[/yellow]"
            )

        # 保存默认配置
        if save_config(DEFAULT_CONFIG):
            console.print(
                Panel.fit(
                    "[green]✅ Configuration reset to defaults![/green]\n\n"
                    "All settings have been restored to their default values.",
                    title="🎉 Reset Complete",
                    border_style="green",
                )
            )
        else:
            console.print("[red]❌ Failed to reset configuration[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]❌ Configuration reset failed: {e}[/red]")
        raise click.Abort()


@config.command()
def edit():
    """Open configuration file in default editor"""
    try:
        # 确保配置文件存在
        if not CONFIG_FILE.exists():
            console.print(
                "[yellow]Configuration file doesn't exist. Creating with defaults...[/yellow]"
            )
            CONFIG_DIR.mkdir(exist_ok=True)
            save_config(DEFAULT_CONFIG)

        # 尝试用不同的编辑器打开
        editors = [
            os.environ.get("EDITOR"),
            "code",  # VS Code
            "nano",
            "vim",
            "vi",
        ]

        for editor in editors:
            if editor:
                try:
                    subprocess.run([editor, str(CONFIG_FILE)])
                    console.print(f"[green]✅ Opened config file with {editor}[/green]")
                    return
                except (FileNotFoundError, subprocess.SubprocessError):
                    continue

        # 如果没有找到编辑器，显示文件路径
        console.print(
            Panel.fit(
                f"[yellow]No suitable editor found.[/yellow]\n\n"
                f"Please edit the config file manually:\n"
                f"[bold]{CONFIG_FILE}[/bold]",
                title="📝 Manual Edit Required",
                border_style="yellow",
            )
        )

    except Exception as e:
        console.print(f"[red]❌ Failed to open editor: {e}[/red]")
        raise click.Abort()
