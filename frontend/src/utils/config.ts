/**
 * Configuration utilities for path prefix and API base URL
 */

/**
 * Get the path prefix for the application
 * This is used for React Router basename and asset paths
 */
export function getPathPrefix(): string {
  // Use compile-time constant if available, otherwise fall back to environment variable
  if (typeof __PATH_PREFIX__ !== 'undefined') {
    return __PATH_PREFIX__;
  }
  
  return import.meta.env.VITE_PATH_PREFIX || '/';
}

/**
 * Get the API base URL
 * Returns empty string for relative URLs in production
 */
export function getApiBaseUrl(): string {
  // Use compile-time constant if available, otherwise fall back to environment variable
  if (typeof __API_BASE_URL__ !== 'undefined') {
    return __API_BASE_URL__;
  }
  
  return import.meta.env.VITE_API_BASE_URL || '';
}

/**
 * Get the full API URL with path prefix
 */
export function getFullApiUrl(): string {
  const apiBaseUrl = getApiBaseUrl();
  const pathPrefix = getPathPrefix();
  
  // If API base URL is empty, use relative path
  if (!apiBaseUrl) {
    return pathPrefix === '/' ? '/api/v1' : `${pathPrefix}api/v1`;
  }
  
  return `${apiBaseUrl}/api/v1`;
}

/**
 * Normalize path prefix to ensure it starts and ends with /
 */
export function normalizePathPrefix(prefix: string): string {
  if (!prefix || prefix === '/') {
    return '/';
  }
  
  // Ensure starts with /
  if (!prefix.startsWith('/')) {
    prefix = '/' + prefix;
  }
  
  // Ensure ends with /
  if (!prefix.endsWith('/')) {
    prefix = prefix + '/';
  }
  
  return prefix;
}

/**
 * Get router basename (path prefix without trailing slash unless it's root)
 */
export function getRouterBasename(): string | undefined {
  const prefix = getPathPrefix();
  if (prefix === '/') {
    return undefined; // Use default for root
  }

  // Remove trailing slash for router basename
  return prefix.endsWith('/') ? prefix.slice(0, -1) : prefix;
}
